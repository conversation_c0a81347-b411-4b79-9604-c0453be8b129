'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { supabase } from '@/lib/supabase';
import { FaStar, FaMapMarkerAlt, FaPhone, FaEnvelope, FaStore, FaCalendarAlt, FaShoppingBag, FaHeart, FaRegHeart, FaShare, FaClock, FaImages, FaComments, FaTruck, FaShoppingCart, FaCheckCircle, FaTimesCircle, FaEye, FaChartLine, FaBox, FaCamera, FaEdit, FaPlus, FaGripVertical, FaPlay, FaCertificate, FaFire, FaTag, FaGift, FaMedal, FaThumbsUp, FaFilter, FaSortAmountDown, FaVideo, FaYoutube, FaUpload, FaBadgeCheck, FaLeaf, FaHandshake, FaAward, FaShieldAlt, FaRocket, FaCrown, FaGem, FaLightbulb, FaBolt, FaStopwatch, FaUserCheck, FaHeadset } from 'react-icons/fa';
import { useOptionalGeolocationContext } from '@/contexts/GeolocationContext';
import { useAuth } from '@/contexts/AuthContext';
import { useCart } from '@/contexts/CartContext';
import StaticMap from '@/components/map/StaticMap';
import toast from 'react-hot-toast';
import { Tab } from '@headlessui/react';

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}

// Fonction utilitaire pour formater la distance
const formatDistanceLocal = (distance: number): string => {
  if (distance < 1) {
    return `${Math.round(distance * 1000)} m`;
  }
  return `${distance.toFixed(1)} km`;
};

// Fonction utilitaire pour calculer la distance
const calculateDistanceLocal = (
  pos1: { lat: number; lng: number },
  pos2: { lat: number; lng: number }
): number => {
  const R = 6371; // Rayon de la Terre en km
  const dLat = (pos2.lat - pos1.lat) * Math.PI / 180;
  const dLng = (pos2.lng - pos1.lng) * Math.PI / 180;
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(pos1.lat * Math.PI / 180) * Math.cos(pos2.lat * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
};

interface SellerProfile {
  id: string;
  name: string;
  avatar_url: string;
  email: string;
  phone: string;
  created_at: string;
  seller_profile: {
    description: string;
    address: string;
    location: { lat: number; lng: number };
    rating: number;
    review_count: number;
    opening_hours: string;
    categories: string[];
    gallery: string[];
    specialties: string[];
    delivery_zones: string[];
    delivery_fee: number;
    min_order: number;
    is_available: boolean;
    response_time: string;
    // Nouvelles fonctionnalités premium
    banner_url?: string;
    slogan?: string;
    story_video_url?: string;
    certifications: Certification[];
    badges: Badge[];
    avg_response_time: number; // en minutes
    satisfaction_rate: number; // pourcentage
    pickup_available: boolean;
    delivery_available: boolean;
    business_hours_today: string;
    next_available: string;
  };
  products: ProductPremium[];
  reviews: Review[];
  stats: {
    total_orders: number;
    total_revenue: number;
    avg_rating: number;
    completion_rate: number;
    // Stats premium
    monthly_orders: number;
    weekly_orders: number;
    best_selling_product: string;
    customer_retention: number;
    avg_order_value: number;
  };
}

interface Certification {
  id: string;
  name: string;
  icon: string;
  description: string;
  verified: boolean;
  date_obtained: string;
}

interface Badge {
  id: string;
  type: 'bio' | 'local' | 'artisan' | 'premium' | 'fast_delivery' | 'top_rated' | 'verified';
  name: string;
  icon: string;
  color: string;
  description: string;
}

interface Review {
  id: string;
  user_name: string;
  user_avatar?: string;
  rating: number;
  comment: string;
  date: string;
  order_items?: string[];
}

interface ProductPremium {
  id: string;
  name: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  stock: number;
  created_at: string;
  // Nouvelles fonctionnalités premium
  badges: ProductBadge[];
  is_new: boolean;
  is_bestseller: boolean;
  is_on_sale: boolean;
  original_price?: number;
  discount_percentage?: number;
  tags: string[];
  gallery: string[];
  variants?: ProductVariant[];
  nutrition_info?: NutritionInfo;
  origin: string;
  harvest_date?: string;
  expiry_date?: string;
  organic_certified: boolean;
  local_certified: boolean;
  seasonal: boolean;
  availability_status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'pre_order';
  estimated_restock?: string;
}

interface ProductBadge {
  type: 'nouveau' | 'promo' | 'bestseller' | 'bio' | 'local' | 'saison' | 'limite';
  label: string;
  color: string;
  icon?: string;
}

interface ProductVariant {
  id: string;
  name: string;
  price: number;
  stock: number;
  attributes: { [key: string]: string }; // ex: { "poids": "1kg", "conditionnement": "barquette" }
}

interface NutritionInfo {
  calories_per_100g: number;
  proteins: number;
  carbs: number;
  fats: number;
  fiber: number;
  vitamins: string[];
}

export default function SellerProfilePage() {
  const t = useTranslations();
  const params = useParams();
  const { user } = useAuth();
  const { addToCart } = useCart();
  const geolocationContext = useOptionalGeolocationContext();
  const { calculateDistance, formatDistance, location } = geolocationContext || {
    calculateDistance: null,
    formatDistance: null,
    location: null
  };

  const [seller, setSeller] = useState<SellerProfile | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState<boolean>(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [distance, setDistance] = useState<number | null>(null);

  // Données de démonstration pour les vendeurs
  const demoSellers: { [key: string]: SellerProfile } = {
    '1': {
      id: '1',
      name: 'Ferme Bio de Thiès',
      avatar_url: '/assets/images/vendors/ferme-bio-thies.jpg',
      email: '<EMAIL>',
      phone: '+221 77 123 45 67',
      created_at: '2023-01-15T00:00:00Z',
      seller_profile: {
        description: 'Spécialiste des légumes biologiques et fruits de saison, cultivés avec passion dans les terres fertiles de Thiès. Nous privilégions les méthodes naturelles et respectueuses de l\'environnement.',
        address: 'Route de Dakar, Thiès, Sénégal',
        location: { lat: 14.7969, lng: -16.9267 },
        rating: 4.9,
        review_count: 127,
        opening_hours: 'Lun-Sam: 7h-18h\nDim: 8h-16h',
        categories: ['Légumes', 'Fruits', 'Bio'],
        gallery: [
          '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
          '/assets/images/products/vegetables/salade-verte-croquante.jpg',
          '/assets/images/products/vegetables/carottes-orange-fraiche.jpg',
          '/assets/images/products/vegetables/choux-verts-bio.jpg'
        ],
        specialties: ['Agriculture biologique', 'Légumes de saison', 'Fruits tropicaux', 'Certification bio'],
        delivery_zones: ['Thiès', 'Dakar', 'Rufisque', 'Pikine'],
        delivery_fee: 1500,
        min_order: 5000,
        is_available: true,
        response_time: '< 2h',
        // Nouvelles fonctionnalités premium
        banner_url: '/assets/images/banners/ferme-bio-banner.jpg',
        slogan: '🌱 Du champ à votre assiette, 100% naturel !',
        story_video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        certifications: [
          {
            id: '1',
            name: 'Agriculture Biologique',
            icon: 'FaLeaf',
            description: 'Certifié par l\'organisme national de certification bio',
            verified: true,
            date_obtained: '2022-03-15'
          },
          {
            id: '2',
            name: 'Commerce Équitable',
            icon: 'FaHandshake',
            description: 'Pratiques commerciales équitables certifiées',
            verified: true,
            date_obtained: '2023-01-10'
          }
        ],
        badges: [
          {
            id: '1',
            type: 'bio',
            name: 'Bio Certifié',
            icon: 'FaLeaf',
            color: 'green',
            description: 'Produits 100% biologiques'
          },
          {
            id: '2',
            type: 'local',
            name: 'Producteur Local',
            icon: 'FaMapMarkerAlt',
            color: 'blue',
            description: 'Produits cultivés localement'
          },
          {
            id: '3',
            type: 'top_rated',
            name: 'Top Vendeur',
            icon: 'FaCrown',
            color: 'gold',
            description: 'Vendeur le mieux noté'
          },
          {
            id: '4',
            type: 'fast_delivery',
            name: 'Livraison Rapide',
            icon: 'FaBolt',
            color: 'orange',
            description: 'Livraison en moins de 2h'
          }
        ],
        avg_response_time: 45, // 45 minutes
        satisfaction_rate: 98.5,
        pickup_available: true,
        delivery_available: true,
        business_hours_today: 'Ouvert jusqu\'à 18h',
        next_available: 'Demain 7h'
      },
      products: [
        {
          id: '1',
          name: 'Tomates Bio 1kg',
          description: 'Tomates biologiques fraîches, cultivées sans pesticides',
          price: 1500,
          image_url: '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
          category: 'Légumes',
          stock: 50,
          created_at: '2024-01-01T00:00:00Z',
          badges: [
            { type: 'bestseller', label: 'Best Seller', color: 'orange', icon: 'FaFire' },
            { type: 'bio', label: 'Bio', color: 'green', icon: 'FaLeaf' }
          ],
          is_new: false,
          is_bestseller: true,
          is_on_sale: false,
          tags: ['bio', 'local', 'frais', 'vitamines'],
          gallery: [
            '/assets/images/products/vegetables/tomates-cerises-bio.jpg',
            '/assets/images/products/vegetables/tomates-cerises-bio-2.jpg'
          ],
          origin: 'Ferme Bio de Thiès',
          harvest_date: '2024-01-14',
          organic_certified: true,
          local_certified: true,
          seasonal: true,
          availability_status: 'in_stock',
          nutrition_info: {
            calories_per_100g: 18,
            proteins: 0.9,
            carbs: 3.9,
            fats: 0.2,
            fiber: 1.2,
            vitamins: ['Vitamine C', 'Vitamine K', 'Folate']
          }
        },
        {
          id: '2',
          name: 'Salade Verte Bio',
          description: 'Salade fraîche du jour, croquante et savoureuse',
          price: 800,
          image_url: '/assets/images/products/vegetables/salade-verte-croquante.jpg',
          category: 'Légumes',
          stock: 30,
          created_at: '2024-01-01T00:00:00Z',
          badges: [
            { type: 'nouveau', label: 'Nouveau', color: 'blue', icon: 'FaRocket' },
            { type: 'bio', label: 'Bio', color: 'green', icon: 'FaLeaf' }
          ],
          is_new: true,
          is_bestseller: false,
          is_on_sale: false,
          tags: ['bio', 'frais', 'croquant', 'hydroponie'],
          gallery: ['/assets/images/products/vegetables/salade-verte-croquante.jpg'],
          origin: 'Jardins Urbains de Dakar',
          harvest_date: '2024-01-15',
          organic_certified: true,
          local_certified: true,
          seasonal: false,
          availability_status: 'in_stock'
        },
        {
          id: '1a',
          name: 'Carottes Bio 1kg',
          description: 'Carottes biologiques croquantes et sucrées',
          price: 1200,
          original_price: 1400,
          discount_percentage: 15,
          image_url: '/assets/images/products/vegetables/carottes-orange-fraiche.jpg',
          category: 'Légumes',
          stock: 40,
          created_at: '2024-01-01T00:00:00Z',
          badges: [
            { type: 'promo', label: '-15%', color: 'red', icon: 'FaTag' },
            { type: 'bio', label: 'Bio', color: 'green', icon: 'FaLeaf' }
          ],
          is_new: false,
          is_bestseller: false,
          is_on_sale: true,
          tags: ['bio', 'promo', 'croquant', 'sucré'],
          gallery: ['/assets/images/products/vegetables/carottes-orange-fraiche.jpg'],
          origin: 'Ferme Bio de Thiès',
          harvest_date: '2024-01-12',
          organic_certified: true,
          local_certified: true,
          seasonal: true,
          availability_status: 'in_stock'
        },
        {
          id: '1b',
          name: 'Choux Verts Bio',
          description: 'Choux verts biologiques riches en vitamines',
          price: 900,
          image_url: '/assets/images/products/vegetables/choux-verts-bio.jpg',
          category: 'Légumes',
          stock: 5,
          created_at: '2024-01-01T00:00:00Z',
          badges: [
            { type: 'limite', label: 'Stock Limité', color: 'yellow', icon: 'FaStopwatch' },
            { type: 'bio', label: 'Bio', color: 'green', icon: 'FaLeaf' }
          ],
          is_new: false,
          is_bestseller: false,
          is_on_sale: false,
          tags: ['bio', 'vitamines', 'stock-limite'],
          gallery: ['/assets/images/products/vegetables/choux-verts-bio.jpg'],
          origin: 'Ferme Bio de Thiès',
          harvest_date: '2024-01-10',
          organic_certified: true,
          local_certified: true,
          seasonal: true,
          availability_status: 'low_stock',
          estimated_restock: '2024-01-20'
        }
      ],
      reviews: [
        {
          id: '1',
          user_name: 'Aminata Diallo',
          user_avatar: '/assets/images/avatars/user1.jpg',
          rating: 5,
          comment: 'Excellente qualité ! Les légumes sont vraiment frais et biologiques. Je recommande vivement cette ferme.',
          date: '2024-01-15T00:00:00Z',
          order_items: ['Tomates Bio', 'Salade Verte']
        },
        {
          id: '2',
          user_name: 'Moussa Sow',
          user_avatar: '/assets/images/avatars/user2.jpg',
          rating: 5,
          comment: 'Livraison rapide et produits de qualité. Les tomates sont délicieuses !',
          date: '2024-01-10T00:00:00Z',
          order_items: ['Tomates Bio', 'Carottes Bio']
        },
        {
          id: '3',
          user_name: 'Fatou Ndiaye',
          rating: 4,
          comment: 'Très bon vendeur, produits frais. Juste un petit retard sur la livraison.',
          date: '2024-01-08T00:00:00Z',
          order_items: ['Salade Verte', 'Choux Verts']
        }
      ],
      stats: {
        total_orders: 342,
        total_revenue: 2850000,
        avg_rating: 4.9,
        completion_rate: 98.5,
        // Stats premium
        monthly_orders: 89,
        weekly_orders: 23,
        best_selling_product: 'Tomates Bio 1kg',
        customer_retention: 87.3,
        avg_order_value: 8333
      }
    },
    '2': {
      id: '2',
      name: 'Boucherie Halal Premium',
      avatar_url: '/assets/images/vendors/boucherie-halal.jpg',
      email: '<EMAIL>',
      phone: '+221 77 234 56 78',
      created_at: '2023-03-20T00:00:00Z',
      seller_profile: {
        description: 'Boucherie halal certifiée proposant des viandes fraîches de qualité supérieure. Nos animaux sont élevés localement selon les traditions halal.',
        address: 'Marché Kermel, Dakar, Sénégal',
        location: { lat: 14.6928, lng: -17.4467 },
        rating: 4.8,
        review_count: 89,
        opening_hours: 'Lun-Sam: 8h-19h\nDim: Fermé',
        categories: ['Viandes', 'Halal'],
        gallery: [
          '/assets/images/products/meat/boeuf-steak-halal.jpg',
          '/assets/images/vendors/boucherie-halal.jpg'
        ],
        specialties: ['Viande halal certifiée', 'Bœuf de zébu local', 'Agneau du Fouta', 'Découpe traditionnelle'],
        delivery_zones: ['Dakar', 'Pikine', 'Guédiawaye', 'Parcelles Assainies'],
        delivery_fee: 2000,
        min_order: 10000,
        is_available: true,
        response_time: '< 1h'
      },
      products: [
        {
          id: '3',
          name: 'Bœuf de Zébu Local 1kg',
          description: 'Viande de bœuf zébu local, élevé en pâturage naturel',
          price: 8500,
          image_url: '/assets/images/products/meat/boeuf-steak-halal.jpg',
          category: 'Viandes',
          stock: 20,
          created_at: '2024-01-01T00:00:00Z'
        }
      ],
      reviews: [
        {
          id: '4',
          user_name: 'Ibrahim Diop',
          rating: 5,
          comment: 'Excellente boucherie ! Viande halal de qualité et service impeccable.',
          date: '2024-01-12T00:00:00Z',
          order_items: ['Bœuf de Zébu']
        },
        {
          id: '5',
          user_name: 'Khadija Ba',
          rating: 4,
          comment: 'Très bonne qualité de viande, prix correct. Je recommande.',
          date: '2024-01-05T00:00:00Z',
          order_items: ['Bœuf de Zébu']
        }
      ],
      stats: {
        total_orders: 156,
        total_revenue: 1250000,
        avg_rating: 4.8,
        completion_rate: 96.2
      }
    }
  };

  // Charger les données du vendeur
  useEffect(() => {
    const fetchSellerData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Utiliser les données de démonstration
        const demoSeller = demoSellers[params.id];

        if (!demoSeller) {
          setError('Vendeur non trouvé');
          setIsLoading(false);
          return;
        }

        setSeller(demoSeller);

        // Calculer la distance si la géolocalisation est disponible
        if (location && demoSeller.seller_profile.location) {
          const distanceFunc = calculateDistance || calculateDistanceLocal;
          const dist = distanceFunc(location, demoSeller.seller_profile.location);
          setDistance(dist);
        }

        // Simuler la vérification des favoris
        setIsFavorite(false);

      } catch (err) {
        console.error('Erreur lors du chargement des données du vendeur:', err);
        setError('Erreur lors du chargement des données du vendeur');
      } finally {
        setIsLoading(false);
      }
    };

    fetchSellerData();
  }, [params.id, user, location]);

  // Filtrer les produits par catégorie
  const filteredProducts = selectedCategory
    ? seller?.products.filter(product => product.category === selectedCategory)
    : seller?.products;

  // Ajouter/supprimer des favoris (version démo)
  const toggleFavorite = async () => {
    if (!user) {
      toast.error('Vous devez être connecté pour ajouter des favoris');
      return;
    }

    try {
      // Simuler l'ajout/suppression des favoris
      if (isFavorite) {
        setIsFavorite(false);
        toast.success('Vendeur retiré des favoris');
      } else {
        setIsFavorite(true);
        toast.success('Vendeur ajouté aux favoris');
      }
    } catch (err) {
      console.error('Erreur lors de la modification des favoris:', err);
      toast.error('Une erreur est survenue');
    }
  };

  // Partager le profil
  const shareSeller = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${seller?.name} sur LocalMarket`,
          text: `Découvrez ${seller?.name} sur LocalMarket`,
          url: window.location.href,
        });
      } catch (err) {
        console.error('Erreur lors du partage:', err);
      }
    } else {
      // Fallback: copier le lien dans le presse-papier
      navigator.clipboard.writeText(window.location.href);
      toast.success('Lien copié dans le presse-papier');
    }
  };

  // Ajouter un produit au panier
  const handleAddToCart = (product: Product) => {
    addToCart({
      id: product.id,
      name: product.name,
      price: product.price,
      quantity: 1,
      image: product.image_url,
      seller_id: seller?.id || '',
      seller_name: seller?.name || '',
    });

    toast.success(`${product.name} ajouté au panier`);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  if (error || !seller) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-900/30 rounded-lg p-6 text-center">
          <h2 className="text-xl font-bold text-red-700 dark:text-red-400 mb-2">
            {error || 'Vendeur non trouvé'}
          </h2>
          <p className="text-red-600 dark:text-red-300 mb-4">
            Impossible de charger les informations du vendeur.
          </p>
          <Link
            href="/sellers"
            className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg"
          >
            Retour à la liste des vendeurs
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* En-tête professionnel avec hiérarchie claire */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Bannière avec overlay professionnel */}
          <div className="relative h-48 md:h-64 bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800">
            {seller.seller_profile.banner_url ? (
              <Image
                src={seller.seller_profile.banner_url}
                alt={`Bannière ${seller.name}`}
                fill
                className="object-cover"
                priority
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <FaStore className="w-24 h-24 md:w-32 md:h-32 text-white/20" />
              </div>
            )}

            {/* Overlay gradient professionnel */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />

            {/* Badges de statut - Position optimisée */}
            <div className="absolute top-4 left-4 flex flex-col sm:flex-row gap-2">
              {seller.seller_profile.is_available ? (
                <div className="bg-green-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-semibold flex items-center shadow-lg">
                  <div className="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
                  <span className="hidden sm:inline">Disponible maintenant</span>
                  <span className="sm:hidden">Ouvert</span>
                </div>
              ) : (
                <div className="bg-red-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-semibold shadow-lg">
                  Fermé
                </div>
              )}

              <div className="bg-blue-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-full text-sm font-semibold flex items-center shadow-lg">
                <FaClock className="w-3 h-3 mr-1" />
                <span className="hidden sm:inline">Répond en {seller.seller_profile.avg_response_time}min</span>
                <span className="sm:hidden">{seller.seller_profile.avg_response_time}min</span>
              </div>
            </div>

            {/* Actions - Boutons professionnels */}
            <div className="absolute top-4 right-4 flex flex-col sm:flex-row gap-2">
              {seller.seller_profile.story_video_url && (
                <button
                  type="button"
                  onClick={() => window.open(seller.seller_profile.story_video_url, '_blank')}
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-sm p-3 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"
                  aria-label="Voir la story vidéo"
                  title="Voir la présentation vidéo"
                >
                  <FaPlay className="text-white w-4 h-4" />
                </button>
              )}

              <button
                type="button"
                onClick={toggleFavorite}
                className="bg-white/10 hover:bg-white/20 backdrop-blur-sm p-3 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"
                aria-label={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}
                title={isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'}
              >
                {isFavorite ? (
                  <FaHeart className="text-red-400 w-4 h-4" />
                ) : (
                  <FaRegHeart className="text-white w-4 h-4" />
                )}
              </button>

              <button
                type="button"
                onClick={shareSeller}
                className="bg-white/10 hover:bg-white/20 backdrop-blur-sm p-3 rounded-full transition-all duration-200 shadow-lg hover:shadow-xl"
                aria-label="Partager ce vendeur"
                title="Partager ce vendeur"
              >
                <FaShare className="text-white w-4 h-4" />
              </button>
            </div>

            {/* Bouton contact principal - Très visible */}
            <div className="absolute bottom-4 right-4">
              <Link
                href={`/chat/${seller.id}`}
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-full font-semibold transition-all duration-200 flex items-center space-x-2 shadow-lg hover:shadow-xl transform hover:scale-105 min-h-[44px]"
              >
                <FaHeadset className="w-5 h-5" />
                <span className="hidden sm:inline">Contacter le vendeur</span>
                <span className="sm:hidden">Contact</span>
              </Link>
            </div>

            {/* Slogan - Typographie améliorée */}
            {seller.seller_profile.slogan && (
              <div className="absolute bottom-4 left-4 right-20 sm:right-40">
                <p className="text-white text-base md:text-lg font-medium bg-black/40 backdrop-blur-sm rounded-xl px-4 py-3 shadow-lg">
                  {seller.seller_profile.slogan}
                </p>
              </div>
            )}
          </div>

          {/* Section informations vendeur - Design professionnel */}
          <div className="relative px-6 py-8 sm:px-8 sm:py-10">
            {/* Avatar avec indicateur de statut */}
            <div className="absolute -top-16 left-6 sm:left-8">
              <div className="relative">
                <div className="w-28 h-28 sm:w-36 sm:h-36 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden bg-white dark:bg-gray-700 shadow-xl">
                  {seller.avatar_url ? (
                    <Image
                      src={seller.avatar_url}
                      alt={seller.name}
                      fill
                      className="object-cover"
                      priority
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400">
                      <FaStore className="w-14 h-14 sm:w-16 sm:h-16" />
                    </div>
                  )}
                </div>
                {/* Indicateur de statut en ligne */}
                <div className={`absolute bottom-2 right-2 w-6 h-6 rounded-full border-3 border-white dark:border-gray-800 ${
                  seller.seller_profile.is_available ? 'bg-green-500' : 'bg-gray-400'
                }`}>
                  {seller.seller_profile.is_available && (
                    <div className="w-full h-full rounded-full bg-green-500 animate-ping opacity-75"></div>
                  )}
                </div>
              </div>
            </div>

            {/* Informations principales - Hiérarchie claire */}
            <div className="mt-16 sm:mt-20">
              <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-6">
                <div className="flex-1">
                  {/* Nom et titre principal */}
                  <div className="mb-4">
                    <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-white font-heading mb-2">
                      {seller.name}
                    </h1>
                    <p className="text-lg text-primary-600 dark:text-primary-400 font-medium">
                      {seller.seller_profile.categories.join(' • ')}
                    </p>
                  </div>

                  {/* Métriques principales - Mise en valeur */}
                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-6">
                    {/* Note et avis */}
                    {seller.seller_profile.rating > 0 && (
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-4 border border-yellow-200 dark:border-yellow-900/30">
                        <div className="flex items-center mb-2">
                          <div className="flex">
                            {[...Array(5)].map((_, i) => (
                              <FaStar
                                key={i}
                                className={`w-5 h-5 ${
                                  i < Math.floor(seller.seller_profile.rating)
                                    ? 'text-yellow-500'
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {seller.seller_profile.rating.toFixed(1)}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {seller.seller_profile.review_count} avis clients
                        </p>
                      </div>
                    )}

                    {/* Distance */}
                    {distance !== null && (
                      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-4 border border-blue-200 dark:border-blue-900/30">
                        <div className="flex items-center mb-2">
                          <FaMapMarkerAlt className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                        </div>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {(formatDistance || formatDistanceLocal)(distance)}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          de votre position
                        </p>
                      </div>
                    )}

                    {/* Ancienneté */}
                    <div className="bg-purple-50 dark:bg-purple-900/20 rounded-xl p-4 border border-purple-200 dark:border-purple-900/30">
                      <div className="flex items-center mb-2">
                        <FaCalendarAlt className="w-5 h-5 text-purple-600 dark:text-purple-400" />
                      </div>
                      <p className="text-2xl font-bold text-gray-900 dark:text-white">
                        {Math.floor((new Date().getTime() - new Date(seller.created_at).getTime()) / (1000 * 60 * 60 * 24 * 365))}+
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        années d'expérience
                      </p>
                    </div>
                  </div>

                  {/* Description - Typographie améliorée */}
                  {seller.seller_profile.description && (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        À propos
                      </h3>
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed text-base">
                        {seller.seller_profile.description}
                      </p>
                    </div>
                  )}

                  {/* Spécialités - Mise en valeur */}
                  {seller.seller_profile.specialties && seller.seller_profile.specialties.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Spécialités
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {seller.seller_profile.specialties.map((specialty, index) => (
                          <span
                            key={index}
                            className="px-4 py-2 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-900/30 text-primary-700 dark:text-primary-400 text-sm font-medium rounded-full border border-primary-200 dark:border-primary-900/30"
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Actions principales - Boutons visibles */}
                <div className="lg:w-80 space-y-4">
                  {/* Bouton contact principal */}
                  <Link
                    href={`/chat/${seller.id}`}
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white px-6 py-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:scale-105 min-h-[56px]"
                  >
                    <FaHeadset className="w-5 h-5" />
                    <span>Contacter le vendeur</span>
                  </Link>

                  {/* Informations de service */}
                  <div className="bg-gray-50 dark:bg-gray-700/50 rounded-xl p-4 space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Livraison</span>
                      <span className="text-sm text-gray-900 dark:text-white font-semibold">
                        {seller.seller_profile.delivery_fee.toLocaleString()} FCFA
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Commande min.</span>
                      <span className="text-sm text-gray-900 dark:text-white font-semibold">
                        {seller.seller_profile.min_order.toLocaleString()} FCFA
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Temps de réponse</span>
                      <span className="text-sm text-gray-900 dark:text-white font-semibold">
                        {seller.seller_profile.response_time}
                      </span>
                    </div>
                  </div>

                  {/* Horaires aujourd'hui */}
                  <div className="bg-green-50 dark:bg-green-900/20 rounded-xl p-4 border border-green-200 dark:border-green-900/30">
                    <div className="flex items-center space-x-2 mb-2">
                      <FaClock className="w-4 h-4 text-green-600 dark:text-green-400" />
                      <span className="text-sm font-medium text-green-700 dark:text-green-400">Aujourd'hui</span>
                    </div>
                    <p className="text-green-900 dark:text-green-300 font-semibold">
                      {seller.seller_profile.business_hours_today}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Badges et certifications - Design professionnel */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="space-y-6">
            {/* Titre de section */}
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                Badges & Certifications
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Reconnaissances et garanties de qualité
              </p>
            </div>

            {/* Badges vendeur - Disposition améliorée */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Badges de qualité
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {seller.seller_profile.badges.map((badge) => (
                  <div
                    key={badge.id}
                    className={`flex flex-col items-center p-4 rounded-xl font-medium text-sm transition-transform hover:scale-105 ${
                      badge.color === 'gold' ? 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-white shadow-yellow-200' :
                      badge.color === 'green' ? 'bg-gradient-to-br from-green-400 to-green-600 text-white shadow-green-200' :
                      badge.color === 'blue' ? 'bg-gradient-to-br from-blue-400 to-blue-600 text-white shadow-blue-200' :
                      badge.color === 'orange' ? 'bg-gradient-to-br from-orange-400 to-orange-600 text-white shadow-orange-200' :
                      'bg-gradient-to-br from-gray-400 to-gray-600 text-white shadow-gray-200'
                    } shadow-lg`}
                    title={badge.description}
                  >
                    <div className="mb-2">
                      {badge.type === 'top_rated' && <FaCrown className="w-6 h-6" />}
                      {badge.type === 'bio' && <FaLeaf className="w-6 h-6" />}
                      {badge.type === 'verified' && <FaShieldAlt className="w-6 h-6" />}
                      {badge.type === 'fast_delivery' && <FaBolt className="w-6 h-6" />}
                      {badge.type === 'local' && <FaMapMarkerAlt className="w-6 h-6" />}
                    </div>
                    <span className="text-center text-xs font-semibold">{badge.name}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Certifications officielles */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                Certifications officielles
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {seller.seller_profile.certifications.map((cert) => (
                  <div
                    key={cert.id}
                    className="flex items-center space-x-4 p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-900/30 hover:shadow-md transition-shadow"
                    title={cert.description}
                  >
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <FaCertificate className="w-6 h-6 text-green-600 dark:text-green-400" />
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-green-900 dark:text-green-300">
                          {cert.name}
                        </h4>
                        {cert.verified && (
                          <FaCheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
                        )}
                      </div>
                      <p className="text-sm text-green-700 dark:text-green-400 mt-1">
                        Obtenu le {new Date(cert.date_obtained).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Statistiques de performance - Design professionnel */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <div className="mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Statistiques de performance
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Indicateurs clés de qualité et de service
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* Commandes totales */}
            <div className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-900/30 rounded-xl p-4 border border-green-200 dark:border-green-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaShoppingCart className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-green-900 dark:text-green-300 mb-1">
                  {seller.stats.total_orders}
                </p>
                <p className="text-xs font-medium text-green-700 dark:text-green-400">
                  Commandes totales
                </p>
              </div>
            </div>

            {/* Chiffre d'affaires */}
            <div className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-900/30 rounded-xl p-4 border border-blue-200 dark:border-blue-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaChartLine className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-blue-900 dark:text-blue-300 mb-1">
                  {(seller.stats.total_revenue / 1000000).toFixed(1)}M
                </p>
                <p className="text-xs font-medium text-blue-700 dark:text-blue-400">
                  Chiffre d'affaires
                </p>
              </div>
            </div>

            {/* Note moyenne */}
            <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 dark:from-yellow-900/20 dark:to-yellow-900/30 rounded-xl p-4 border border-yellow-200 dark:border-yellow-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaStar className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-yellow-900 dark:text-yellow-300 mb-1">
                  {seller.stats.avg_rating.toFixed(1)}
                </p>
                <p className="text-xs font-medium text-yellow-700 dark:text-yellow-400">
                  Note moyenne
                </p>
              </div>
            </div>

            {/* Satisfaction */}
            <div className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-900/30 rounded-xl p-4 border border-purple-200 dark:border-purple-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaThumbsUp className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-purple-900 dark:text-purple-300 mb-1">
                  {seller.seller_profile.satisfaction_rate}%
                </p>
                <p className="text-xs font-medium text-purple-700 dark:text-purple-400">
                  Satisfaction
                </p>
              </div>
            </div>

            {/* Temps de réponse */}
            <div className="bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-900/30 rounded-xl p-4 border border-orange-200 dark:border-orange-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaClock className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-orange-900 dark:text-orange-300 mb-1">
                  {seller.seller_profile.avg_response_time}min
                </p>
                <p className="text-xs font-medium text-orange-700 dark:text-orange-400">
                  Temps réponse
                </p>
              </div>
            </div>

            {/* Fidélisation */}
            <div className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-900/30 rounded-xl p-4 border border-red-200 dark:border-red-900/30">
              <div className="text-center">
                <div className="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                  <FaUserCheck className="text-white w-6 h-6" />
                </div>
                <p className="text-2xl font-bold text-red-900 dark:text-red-300 mb-1">
                  {seller.stats.customer_retention}%
                </p>
                <p className="text-xs font-medium text-red-700 dark:text-red-400">
                  Fidélisation
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Navigation par onglets - Design professionnel */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <Tab.Group>
            {/* Navigation améliorée */}
            <Tab.List className="flex flex-wrap sm:flex-nowrap bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-2 mb-6 border border-gray-200 dark:border-gray-700">
              <Tab
                className={({ selected }) =>
                  classNames(
                    'flex-1 min-w-0 rounded-xl py-3 px-4 text-sm font-semibold leading-5 transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                    selected
                      ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                  )
                }
              >
                <div className="flex items-center justify-center space-x-2">
                  <FaBox className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">Produits</span>
                  <span className="hidden sm:inline bg-white/20 text-xs px-2 py-1 rounded-full">
                    {seller.products.length}
                  </span>
                </div>
              </Tab>

              <Tab
                className={({ selected }) =>
                  classNames(
                    'flex-1 min-w-0 rounded-xl py-3 px-4 text-sm font-semibold leading-5 transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                    selected
                      ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                  )
                }
              >
                <div className="flex items-center justify-center space-x-2">
                  <FaImages className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">Galerie</span>
                  <span className="hidden sm:inline bg-white/20 text-xs px-2 py-1 rounded-full">
                    {seller.seller_profile.gallery.length}
                  </span>
                </div>
              </Tab>

              <Tab
                className={({ selected }) =>
                  classNames(
                    'flex-1 min-w-0 rounded-xl py-3 px-4 text-sm font-semibold leading-5 transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                    selected
                      ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                  )
                }
              >
                <div className="flex items-center justify-center space-x-2">
                  <FaComments className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate">Avis</span>
                  <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
                    {seller.reviews.length}
                  </span>
                </div>
              </Tab>

              <Tab
                className={({ selected }) =>
                  classNames(
                    'flex-1 min-w-0 rounded-xl py-3 px-4 text-sm font-semibold leading-5 transition-all duration-200',
                    'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
                    selected
                      ? 'bg-primary-600 text-white shadow-lg transform scale-105'
                      : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-200'
                  )
                }
              >
                <div className="flex items-center justify-center space-x-2">
                  <FaMapMarkerAlt className="w-4 h-4 flex-shrink-0" />
                  <span className="truncate hidden sm:inline">Infos & Livraison</span>
                  <span className="truncate sm:hidden">Infos</span>
                </div>
              </Tab>
            </Tab.List>

          <Tab.Panels>
            {/* Onglet Produits */}
            <Tab.Panel>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                {/* Colonne de gauche: Informations de contact et carte */}
                <motion.div
                  className="lg:col-span-1"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
          {/* Informations de contact */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 mb-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
              Informations de contact
            </h2>

            <div className="space-y-4">
              {seller.phone && (
                <div className="flex items-start">
                  <FaPhone className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Téléphone</p>
                    <p className="text-gray-900 dark:text-white">{seller.phone}</p>
                  </div>
                </div>
              )}

              {seller.email && (
                <div className="flex items-start">
                  <FaEnvelope className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                    <p className="text-gray-900 dark:text-white">{seller.email}</p>
                  </div>
                </div>
              )}

              {seller.seller_profile.address && (
                <div className="flex items-start">
                  <FaMapMarkerAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Adresse</p>
                    <p className="text-gray-900 dark:text-white">{seller.seller_profile.address}</p>
                  </div>
                </div>
              )}

              {seller.seller_profile.opening_hours && (
                <div className="flex items-start">
                  <FaCalendarAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Horaires d'ouverture</p>
                    <p className="text-gray-900 dark:text-white whitespace-pre-line">
                      {seller.seller_profile.opening_hours}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Carte */}
          {seller.seller_profile.location && seller.seller_profile.location.lat !== 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                Emplacement
              </h2>

              <StaticMap
                location={seller.seller_profile.location}
                height="250px"
                markerTitle={seller.name}
                className="rounded-lg overflow-hidden"
              />

              <div className="mt-4">
                <Link
                  href={`https://www.google.com/maps/dir/?api=1&destination=${seller.seller_profile.location.lat},${seller.seller_profile.location.lng}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block w-full bg-primary-600 hover:bg-primary-700 text-white text-center px-4 py-2 rounded-lg"
                >
                  Itinéraire
                </Link>
              </div>
            </div>
          )}
        </motion.div>

        {/* Colonne de droite: Produits */}
        <motion.div
          className="lg:col-span-2"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
            <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4 mb-6">
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading">
                  Produits ({filteredProducts?.length || 0})
                </h2>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Produit vedette: {seller.stats.best_selling_product}
                </p>
              </div>

              {/* Filtres et tri premium */}
              <div className="flex flex-wrap gap-3">
                {/* Filtre par badges */}
                <div className="flex items-center space-x-2">
                  <button
                    type="button"
                    onClick={() => setSelectedCategory(null)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      !selectedCategory
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    Tous
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedCategory('bestseller')}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${
                      selectedCategory === 'bestseller'
                        ? 'bg-orange-600 text-white'
                        : 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-400 hover:bg-orange-200 dark:hover:bg-orange-900/50'
                    }`}
                  >
                    <FaFire className="w-3 h-3" />
                    <span>Best Sellers</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedCategory('nouveau')}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${
                      selectedCategory === 'nouveau'
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 hover:bg-blue-200 dark:hover:bg-blue-900/50'
                    }`}
                  >
                    <FaRocket className="w-3 h-3" />
                    <span>Nouveautés</span>
                  </button>
                  <button
                    type="button"
                    onClick={() => setSelectedCategory('promo')}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors flex items-center space-x-1 ${
                      selectedCategory === 'promo'
                        ? 'bg-red-600 text-white'
                        : 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 hover:bg-red-200 dark:hover:bg-red-900/50'
                    }`}
                  >
                    <FaTag className="w-3 h-3" />
                    <span>Promos</span>
                  </button>
                </div>

                {/* Tri */}
                <div className="relative">
                  <select
                    className="bg-gray-100 dark:bg-gray-700 border-0 text-gray-700 dark:text-gray-300 rounded-lg py-2 pl-3 pr-8 appearance-none focus:outline-none focus:ring-2 focus:ring-primary-500"
                    aria-label="Trier les produits"
                  >
                    <option value="name">Nom A-Z</option>
                    <option value="price_asc">Prix croissant</option>
                    <option value="price_desc">Prix décroissant</option>
                    <option value="newest">Plus récents</option>
                    <option value="popular">Plus populaires</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                    <FaSortAmountDown className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                  </div>
                </div>
              </div>
            </div>

            {filteredProducts && filteredProducts.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProducts.map((product) => (
                  <motion.div
                    key={product.id}
                    className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 group"
                    whileHover={{ y: -4 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="relative h-48 bg-gray-200 dark:bg-gray-700 overflow-hidden">
                      {product.image_url ? (
                        <Image
                          src={product.image_url}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      ) : (
                        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400 dark:text-gray-500">
                          <FaShoppingBag className="w-12 h-12" />
                        </div>
                      )}

                      {/* Badges premium */}
                      <div className="absolute top-3 left-3 flex flex-col gap-2">
                        {product.badges.map((badge, index) => (
                          <div
                            key={index}
                            className={`px-2 py-1 rounded-full text-xs font-bold flex items-center space-x-1 ${
                              badge.color === 'orange' ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white' :
                              badge.color === 'blue' ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white' :
                              badge.color === 'red' ? 'bg-gradient-to-r from-red-500 to-pink-500 text-white' :
                              badge.color === 'green' ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white' :
                              badge.color === 'yellow' ? 'bg-gradient-to-r from-yellow-500 to-orange-500 text-white' :
                              'bg-gradient-to-r from-gray-500 to-gray-600 text-white'
                            }`}
                          >
                            {badge.type === 'bestseller' && <FaFire className="w-3 h-3" />}
                            {badge.type === 'nouveau' && <FaRocket className="w-3 h-3" />}
                            {badge.type === 'promo' && <FaTag className="w-3 h-3" />}
                            {badge.type === 'bio' && <FaLeaf className="w-3 h-3" />}
                            {badge.type === 'limite' && <FaStopwatch className="w-3 h-3" />}
                            <span>{badge.label}</span>
                          </div>
                        ))}
                      </div>

                      {/* Statut de stock */}
                      <div className="absolute top-3 right-3">
                        <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                          product.availability_status === 'in_stock' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                          product.availability_status === 'low_stock' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                          product.availability_status === 'out_of_stock' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                          'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400'
                        }`}>
                          {product.availability_status === 'in_stock' && `${product.stock} en stock`}
                          {product.availability_status === 'low_stock' && `${product.stock} restants`}
                          {product.availability_status === 'out_of_stock' && 'Épuisé'}
                          {product.availability_status === 'pre_order' && 'Pré-commande'}
                        </div>
                      </div>

                      {/* Overlay avec actions rapides */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex space-x-2">
                          <button
                            type="button"
                            className="p-2 bg-white/90 rounded-full text-gray-700 hover:bg-white transition-colors"
                            aria-label="Voir les détails"
                          >
                            <FaEye className="w-4 h-4" />
                          </button>
                          <button
                            type="button"
                            className="p-2 bg-white/90 rounded-full text-gray-700 hover:bg-white transition-colors"
                            aria-label="Ajouter aux favoris"
                          >
                            <FaHeart className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    <div className="p-5">
                      {/* En-tête produit */}
                      <div className="mb-3">
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-1 line-clamp-1">
                          {product.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {product.description}
                        </p>
                      </div>

                      {/* Tags produit */}
                      {product.tags && product.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {product.tags.slice(0, 3).map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}

                      {/* Informations premium */}
                      <div className="space-y-2 mb-4">
                        {product.origin && (
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <FaMapMarkerAlt className="w-3 h-3 mr-1" />
                            <span>Origine: {product.origin}</span>
                          </div>
                        )}
                        {product.harvest_date && (
                          <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                            <FaCalendarAlt className="w-3 h-3 mr-1" />
                            <span>Récolté le {new Date(product.harvest_date).toLocaleDateString()}</span>
                          </div>
                        )}
                        {product.organic_certified && (
                          <div className="flex items-center text-xs text-green-600 dark:text-green-400">
                            <FaLeaf className="w-3 h-3 mr-1" />
                            <span>Certifié biologique</span>
                          </div>
                        )}
                      </div>

                      {/* Prix et action */}
                      <div className="flex justify-between items-center">
                        <div>
                          {product.is_on_sale && product.original_price ? (
                            <div className="flex items-center space-x-2">
                              <span className="font-bold text-red-600 dark:text-red-400">
                                {product.price.toLocaleString()} FCFA
                              </span>
                              <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                                {product.original_price.toLocaleString()} FCFA
                              </span>
                            </div>
                          ) : (
                            <span className="font-bold text-gray-900 dark:text-white">
                              {product.price.toLocaleString()} FCFA
                            </span>
                          )}
                        </div>

                        <button
                          type="button"
                          onClick={() => handleAddToCart(product)}
                          disabled={product.availability_status === 'out_of_stock'}
                          className="p-2 rounded-full bg-primary-600 text-white hover:bg-primary-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          aria-label="Ajouter au panier"
                        >
                          <FaShoppingCart className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <FaShoppingBag className="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Aucun produit disponible
                </h3>
                <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
                  Ce vendeur n'a pas encore ajouté de produits ou aucun produit ne correspond à la catégorie sélectionnée.
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
            </Tab.Panel>

            {/* Onglet Galerie */}
            <Tab.Panel>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-6">
                  Galerie Photos
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {seller.seller_profile.gallery.map((image, index) => (
                    <div key={index} className="relative aspect-square rounded-lg overflow-hidden group cursor-pointer">
                      <Image
                        src={image}
                        alt={`Photo ${index + 1}`}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                        <FaEye className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Tab.Panel>

            {/* Onglet Avis */}
            <Tab.Panel>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-6">
                  Avis Clients ({seller.reviews.length})
                </h2>
                <div className="space-y-6">
                  {seller.reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          {review.user_avatar ? (
                            <Image
                              src={review.user_avatar}
                              alt={review.user_name}
                              width={40}
                              height={40}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {review.user_name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900 dark:text-white">
                              {review.user_name}
                            </h4>
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {new Date(review.date).toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex items-center mt-1">
                            {[...Array(5)].map((_, i) => (
                              <FaStar
                                key={i}
                                className={`w-4 h-4 ${
                                  i < review.rating
                                    ? 'text-yellow-500'
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                          <p className="mt-2 text-gray-600 dark:text-gray-300">
                            {review.comment}
                          </p>
                          {review.order_items && review.order_items.length > 0 && (
                            <div className="mt-2 flex flex-wrap gap-2">
                              {review.order_items.map((item, index) => (
                                <span
                                  key={index}
                                  className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 rounded"
                                >
                                  {item}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </Tab.Panel>

            {/* Onglet Infos & Livraison */}
            <Tab.Panel>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Informations de contact */}
                <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                    Informations de contact
                  </h2>

                  <div className="space-y-4">
                    {seller.phone && (
                      <div className="flex items-start">
                        <FaPhone className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Téléphone</p>
                          <p className="text-gray-900 dark:text-white">{seller.phone}</p>
                        </div>
                      </div>
                    )}

                    {seller.email && (
                      <div className="flex items-start">
                        <FaEnvelope className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                          <p className="text-gray-900 dark:text-white">{seller.email}</p>
                        </div>
                      </div>
                    )}

                    {seller.seller_profile.address && (
                      <div className="flex items-start">
                        <FaMapMarkerAlt className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Adresse</p>
                          <p className="text-gray-900 dark:text-white">{seller.seller_profile.address}</p>
                        </div>
                      </div>
                    )}

                    {seller.seller_profile.opening_hours && (
                      <div className="flex items-start">
                        <FaClock className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Horaires d'ouverture</p>
                          <p className="text-gray-900 dark:text-white whitespace-pre-line">
                            {seller.seller_profile.opening_hours}
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="flex items-start">
                      <FaClock className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Temps de réponse</p>
                        <p className="text-gray-900 dark:text-white">{seller.seller_profile.response_time}</p>
                      </div>
                    </div>

                    <div className="flex items-start">
                      <div className={`w-3 h-3 rounded-full mt-2 mr-3 ${seller.seller_profile.is_available ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-400">Statut</p>
                        <p className="text-gray-900 dark:text-white">
                          {seller.seller_profile.is_available ? 'Disponible' : 'Indisponible'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Spécialités */}
                  <div className="mt-6">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Spécialités
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {seller.seller_profile.specialties.map((specialty, index) => (
                        <span
                          key={index}
                          className="px-3 py-1 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 text-sm rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Informations de livraison */}
                <div className="space-y-6">
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                    <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                      Livraison
                    </h2>

                    <div className="space-y-4">
                      <div className="flex items-start">
                        <FaTruck className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Frais de livraison</p>
                          <p className="text-gray-900 dark:text-white">
                            {seller.seller_profile.delivery_fee.toLocaleString()} FCFA
                          </p>
                        </div>
                      </div>

                      <div className="flex items-start">
                        <FaShoppingCart className="text-primary-600 dark:text-primary-400 mt-1 mr-3" />
                        <div>
                          <p className="text-sm text-gray-500 dark:text-gray-400">Commande minimum</p>
                          <p className="text-gray-900 dark:text-white">
                            {seller.seller_profile.min_order.toLocaleString()} FCFA
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Zones de livraison
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {seller.seller_profile.delivery_zones.map((zone, index) => (
                          <span
                            key={index}
                            className="px-3 py-1 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-sm rounded-full"
                          >
                            {zone}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Carte */}
                  {seller.seller_profile.location && seller.seller_profile.location.lat !== 0 && (
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6">
                      <h2 className="text-xl font-bold text-gray-900 dark:text-white font-heading mb-4">
                        Emplacement
                      </h2>

                      <StaticMap
                        location={seller.seller_profile.location}
                        height="300px"
                        markerTitle={seller.name}
                        className="rounded-lg overflow-hidden"
                      />

                      {distance !== null && (
                        <div className="mt-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <FaMapMarkerAlt className="mr-2" />
                          Distance: {(formatDistance || formatDistanceLocal)(distance)}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Tab.Panel>
          </Tab.Panels>
        </Tab.Group>
      </motion.div>
      </div>
    </div>
  );
}
