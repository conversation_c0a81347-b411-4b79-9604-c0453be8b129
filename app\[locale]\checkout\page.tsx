"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useTranslations } from "next-intl";
import { useCart } from "@/contexts/CartContext";
import { useAuth } from "@/hooks/useAuth";
import { useNotifications } from "@/hooks/useNotifications";
import { PaymentMethod, initiatePayment } from "@/lib/payment";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/Button";
import PaymentMethodSelector from "@/components/checkout/PaymentMethodSelector";
import { FaArrowLeft, FaSpinner } from "react-icons/fa";
import Link from "next/link";

const CheckoutPage = () => {
  const t = useTranslations("checkout");
  const buttonT = useTranslations("buttons");
  const router = useRouter();
  const { items, totalPrice, clearCart } = useCart();

  // Utilisation de données de démonstration pour éviter les problèmes d'auth
  const user = {
    id: 'demo-user-1',
    name: 'Utilisateur Démo',
    email: '<EMAIL>',
    phone: '+221 77 123 45 67'
  };
  const isAuthenticated = true;
  const isLoading = false;

  const { addNotification } = useNotifications();

  // États
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>("wave");
  const [isProcessing, setIsProcessing] = useState(false);
  const [fullName, setFullName] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [city, setCity] = useState("");
  const [additionalInfo, setAdditionalInfo] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Vérifier si l'utilisateur est authentifié
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      addNotification({
        title: t("loginRequired"),
        message: t("loginRequiredMessage"),
        type: "warning",
      });
      router.push(`/auth/login?redirect=/checkout`);
    }
  }, [isLoading, isAuthenticated, router, addNotification, t]);

  // Vérifier si le panier est vide
  useEffect(() => {
    if (!isLoading && items.length === 0) {
      addNotification({
        title: t("emptyCart"),
        message: t("emptyCartMessage"),
        type: "warning",
      });
      router.push("/cart");
    }
  }, [isLoading, items, router, addNotification, t]);

  // Charger les informations de l'utilisateur (version démo)
  useEffect(() => {
    if (user) {
      setFullName(user.name || "");
      setPhone(user.phone || "");

      // Utiliser une adresse de démonstration
      setAddress("Rue 10 x Avenue Bourguiba");
      setCity("Dakar");
    }
  }, [user]);

  // Valider le formulaire
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!fullName.trim()) {
      newErrors.fullName = t("requiredField");
    }

    if (!phone.trim()) {
      newErrors.phone = t("requiredField");
    } else if (!/^\d{9,}$/.test(phone.replace(/\s/g, ""))) {
      newErrors.phone = t("invalidPhone");
    }

    if (!address.trim()) {
      newErrors.address = t("requiredField");
    }

    if (!city.trim()) {
      newErrors.city = t("requiredField");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Gérer la soumission du formulaire
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);

    try {
      // Simuler la création de commande
      const order = {
        id: `demo-order-${Date.now()}`,
        user_id: user?.id,
        total_amount: totalPrice + 1000, // Ajouter les frais de livraison
        status: "pending",
        payment_status: "pending",
        delivery_address: address,
        delivery_city: city,
        delivery_info: additionalInfo,
        customer_name: fullName,
        customer_phone: phone,
        created_at: new Date().toISOString()
      };

      // Simuler l'ajout des articles de commande
      const orderItems = items.map((item) => ({
        order_id: order.id,
        product_id: item.product_id,
        quantity: item.quantity,
        price: item.price,
        seller_id: item.seller_id,
      }));

      console.log('Commande créée (démo):', order);
      console.log('Articles de commande (démo):', orderItems);

      // Simuler l'initialisation du paiement
      const paymentResponse = {
        success: true,
        redirectUrl: null,
        transactionId: `demo-tx-${Date.now()}`
      };

      console.log('Paiement initié (démo):', {
        amount: order.total_amount,
        currency: "XOF",
        method: paymentMethod,
        orderId: order.id,
        customerEmail: user?.email || "",
        customerName: fullName,
        customerPhone: phone,
        description: `Commande #${order.id}`,
      });

      // Vider le panier
      await clearCart();

      // Afficher une notification de succès
      addNotification({
        title: "Commande créée avec succès !",
        message: `Votre commande #${order.id} a été créée. Redirection vers le paiement...`,
        type: "success",
      });

      // Simuler un délai puis rediriger vers la page de succès
      setTimeout(() => {
        router.push(`/checkout/success?orderId=${order.id}`);
      }, 2000);
    } catch (error) {
      console.error("Erreur lors de la création de la commande:", error);
      addNotification({
        title: t("orderError"),
        message: error instanceof Error ? error.message : String(error),
        type: "error",
      });
      setIsProcessing(false);
    }
  };

  // Si les données sont en cours de chargement, afficher un indicateur de chargement
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  // Si l'utilisateur n'est pas authentifié ou le panier est vide, ne pas afficher le contenu
  if (!isAuthenticated || items.length === 0) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
        {t("title")}
      </h1>

      <div className="mb-4">
        <Link
          href="/cart"
          className="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 flex items-center text-sm font-medium"
        >
          <FaArrowLeft className="mr-1.5 h-4 w-4" />
          {buttonT("back")}
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Formulaire de commande */}
        <div className="lg:col-span-2">
          <form onSubmit={handleSubmit}>
            {/* Informations de livraison */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {t("deliveryInfo")}
                </h2>
              </div>

              <div className="p-6 space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label
                      htmlFor="fullName"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      {t("fullName")} *
                    </label>
                    <input
                      type="text"
                      id="fullName"
                      value={fullName}
                      onChange={(e) => setFullName(e.target.value)}
                      className={`w-full px-4 py-2 border ${
                        errors.fullName
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                    />
                    {errors.fullName && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.fullName}
                      </p>
                    )}
                  </div>

                  <div>
                    <label
                      htmlFor="phone"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      {t("phone")} *
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className={`w-full px-4 py-2 border ${
                        errors.phone
                          ? "border-red-500"
                          : "border-gray-300 dark:border-gray-600"
                      } rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                    />
                    {errors.phone && (
                      <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                        {errors.phone}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("address")} *
                  </label>
                  <input
                    type="text"
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    className={`w-full px-4 py-2 border ${
                      errors.address
                        ? "border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    } rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                  />
                  {errors.address && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.address}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="city"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("city")} *
                  </label>
                  <input
                    type="text"
                    id="city"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    className={`w-full px-4 py-2 border ${
                      errors.city
                        ? "border-red-500"
                        : "border-gray-300 dark:border-gray-600"
                    } rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white`}
                  />
                  {errors.city && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {errors.city}
                    </p>
                  )}
                </div>

                <div>
                  <label
                    htmlFor="additionalInfo"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    {t("additionalInfo")}
                  </label>
                  <textarea
                    id="additionalInfo"
                    value={additionalInfo}
                    onChange={(e) => setAdditionalInfo(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                  ></textarea>
                </div>
              </div>
            </div>

            {/* Méthode de paiement */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-8">
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                  {t("paymentMethod")}
                </h2>
              </div>

              <div className="p-6">
                <PaymentMethodSelector
                  selectedMethod={paymentMethod}
                  onSelectMethod={setPaymentMethod}
                  disabled={isProcessing}
                />
              </div>
            </div>

            {/* Bouton de commande */}
            <div className="flex justify-end">
              <Button
                type="submit"
                variant="primary"
                className="px-8 py-3"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <span className="flex items-center">
                    <FaSpinner className="animate-spin mr-2" />
                    {t("paymentProcessing")}
                  </span>
                ) : (
                  t("placeOrder")
                )}
              </Button>
            </div>
          </form>
        </div>

        {/* Récapitulatif de la commande */}
        <div className="lg:col-span-1">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden sticky top-24">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {t("orderSummary")}
              </h2>
            </div>

            <div className="p-6">
              <div className="space-y-4">
                {items.map((item) => (
                  <div key={item.product_id} className="flex justify-between">
                    <div className="flex-1">
                      <p className="text-gray-900 dark:text-white font-medium">
                        {item.name}
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm">
                        {item.quantity} x{" "}
                        {new Intl.NumberFormat("fr-SN", {
                          style: "currency",
                          currency: "XOF",
                          minimumFractionDigits: 0,
                        }).format(item.price)}
                      </p>
                    </div>
                    <div className="text-gray-900 dark:text-white font-medium">
                      {new Intl.NumberFormat("fr-SN", {
                        style: "currency",
                        currency: "XOF",
                        minimumFractionDigits: 0,
                      }).format(item.price * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>

              <div className="border-t border-gray-200 dark:border-gray-700 mt-6 pt-6 space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Sous-total
                  </span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {new Intl.NumberFormat("fr-SN", {
                      style: "currency",
                      currency: "XOF",
                      minimumFractionDigits: 0,
                    }).format(totalPrice)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">
                    Frais de livraison
                  </span>
                  <span className="text-gray-900 dark:text-white font-medium">
                    {new Intl.NumberFormat("fr-SN", {
                      style: "currency",
                      currency: "XOF",
                      minimumFractionDigits: 0,
                    }).format(1000)}
                  </span>
                </div>

                <div className="border-t border-gray-200 dark:border-gray-700 pt-4 flex justify-between font-bold">
                  <span className="text-gray-900 dark:text-white">Total</span>
                  <span className="text-gray-900 dark:text-white">
                    {new Intl.NumberFormat("fr-SN", {
                      style: "currency",
                      currency: "XOF",
                      minimumFractionDigits: 0,
                    }).format(totalPrice + 1000)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
